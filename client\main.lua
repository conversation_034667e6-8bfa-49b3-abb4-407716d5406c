-- إعلانات المتغيرات المحسنة مع النطاق المناسب
local missionState = {
    currentMission = nil,
    deliveryPed = nil,
    deliveryBlips = {},
    hasEnteredDeliveryZone = false,
    missionStartTime = nil,
    monitoringActive = false,
    showTimer = false,
    deliveryLocation = nil,
    timerThread = nil,
    monitorThread = nil
}

-- دالة لإرفاق حقيبة المهمة للاعب (باستخدام نظام الملابس)
local function AttachMissionBag()
    local playerPed = PlayerPedId()

    -- تعيين ملابس حقيبة المهمة (حقيبة من محل الملابس)
    SetPedComponentVariation(
        playerPed,
        Najm.MissionBag.componentId,  -- معرف المكون (5 = حقائب/باراشوت)
        Najm.MissionBag.drawableId,   -- معرف الرسم (44 = حقيبة من محل الملابس)
        Najm.MissionBag.textureId,    -- معرف النسيج
        0                             -- معرف اللوحة
    )

    if Najm.Debug then
        print('[تشخيص] تم إرفاق حقيبة المهمة - الرسم: ' .. Najm.MissionBag.drawableId)
    end
end

-- دالة لإزالة حقيبة المهمة من اللاعب (تعيين للافتراضي 0, 0)
local function DetachMissionBag()
    local playerPed = PlayerPedId()

    -- إزالة الحقيبة بتعيين القيم الافتراضية (0, 0)
    SetPedComponentVariation(
        playerPed,
        Najm.MissionBag.componentId,  -- معرف المكون (5 = حقائب/باراشوت)
        0,                            -- الرسم الافتراضي (بدون حقيبة)
        0,                            -- النسيج الافتراضي
        0                             -- معرف اللوحة
    )

    if Najm.Debug then
        print('[تشخيص] تم إزالة حقيبة المهمة - تم التعيين للافتراضي (0, 0)')
    end
end

-- دالة لتنسيق الوقت للعرض
local function FormatTime(seconds)
    local minutes = math.floor(seconds / 60)
    local remainingSeconds = seconds % 60
    return string.format("%02d:%02d", minutes, remainingSeconds)
end

-- عرض المؤقت المحسن مع تقليل استخدام المعالج
local function DisplayMissionTimer()
    if missionState.timerThread then return end -- منع الخيوط المكررة

    missionState.timerThread = CreateThread(function()
        local lastUpdate = 0
        local cachedTimeText = ""

        while missionState.showTimer and missionState.currentMission do
            local currentTime = GetGameTimer()

            -- تحديث نص المؤقت مرة واحدة فقط في الثانية لتقليل استخدام المعالج
            if currentTime - lastUpdate >= 1000 then
                if missionState.missionStartTime then
                    local elapsedTime = (currentTime - missionState.missionStartTime) / 1000
                    local remainingTime = (Najm.MissionDuration * 60) - elapsedTime

                    if remainingTime > 0 then
                        cachedTimeText = FormatTime(math.ceil(remainingTime))
                        lastUpdate = currentTime
                    else
                        -- انتهى الوقت، يجب أن تفشل المهمة
                        missionState.showTimer = false
                        break
                    end
                end
            end

            -- الرسم في كل إطار ولكن مع النص المخزن مؤقتاً
            if missionState.showTimer and cachedTimeText ~= "" then
                -- رسم صندوق الخلفية أولاً
                DrawRect(0.5, 0.05, 0.2, 0.04, 0, 0, 0, 150)

                -- عرض المؤقت على الشاشة مع رسم النص المحسن
                SetTextFont(0)
                SetTextProportional(1)
                SetTextScale(0.4, 0.4)
                SetTextColour(255, 255, 255, 255)
                SetTextDropshadow(0, 0, 0, 0, 255)
                SetTextEdge(1, 0, 0, 0, 255)
                SetTextDropShadow()
                SetTextOutline()
                SetTextCentre(true)
                SetTextEntry("STRING")
                AddTextComponentString('<FONT FACE = "A9eelsh"> ' ..  cachedTimeText .. ' :ﻲﻘﺒﺘﻤﻟﺍ ﺔﻤﻬﻤﻟﺍ ﺖﻗﻭ' )
                DrawText(0.5, 0.04)
            end

            Wait(0) -- الرسم في كل إطار
        end

        missionState.timerThread = nil -- تنظيف مرجع الخيط
    end)
end

-- إعلان مسبق
local CleanupMissionData

-- دالة للتعامل مع فشل المهمة
local function FailMission(reason)
    if not missionState.currentMission then return end

    -- إيقاف مراقبة المهمة والمؤقت
    missionState.monitoringActive = false
    missionState.showTimer = false

    -- إرسال إشعار الفشل مع رسالة عربية
    TriggerEvent("pNotify:SendNotification", {
        text = "<h1 style=\"color: #FF0000;\"><center>فشلت المهمة!</center></h1>" ..
               "<font color=\"white\" size=\"4\"><p align=\"right\"><b>السبب: " .. reason .. "</p>" ..
               "<font color=\"red\" size=\"4\"><p align=\"right\"><b>ثقتنا مو في محلها... ما نقدر نعتمد عليك!</p>" ..
               "<font color=\"yellow\" size=\"4\"><p align=\"right\"><b>حاول مرة ثانية بعد فترة وأثبت إنك تستاهل الثقة.</p>",
        type = "error",
        queue = "mission_failure",
        timeout = 10000,
        layout = "CenterLeft",
        killer = true
    })

    -- تنظيف بيانات المهمة أولاً
    CleanupMissionData()

    -- إشعار الخادم بفشل المهمة
    TriggerServerEvent('BlackMarketMission:Server:FailMission', reason)
end

-- مراقبة المهمة المحسنة مع الاستطلاع الفعال
local function StartMissionMonitoring()
    if missionState.monitoringActive then return end

    missionState.monitoringActive = true
    missionState.missionStartTime = GetGameTimer()
    missionState.showTimer = true

    -- بدء عرض المؤقت
    DisplayMissionTimer()

    if missionState.monitorThread then return end -- منع الخيوط المكررة

    missionState.monitorThread = CreateThread(function()
        local playerPed = PlayerPedId()
        local lastHealthCheck = 0
        local lastTimeCheck = 0

        while missionState.monitoringActive and missionState.currentMission do
            local currentTime = GetGameTimer()

            -- فحص صحة/حالة اللاعب كل ثانيتين بدلاً من كل ثانية
            if currentTime - lastHealthCheck >= 2000 then
                -- تخزين PlayerPedId() مؤقتاً لتجنب الاستدعاءات المتكررة
                if not DoesEntityExist(playerPed) then
                    playerPed = PlayerPedId()
                end

                -- فحص ما إذا كان اللاعب ميتاً
                if Najm.MissionFailure.checkDeath and IsEntityDead(playerPed) then
                    FailMission("موت اللاعب")
                    break
                end

                -- فحص ما إذا كان اللاعب مقيداً
                if Najm.MissionFailure.checkHandcuffed and IsPedCuffed(playerPed) then
                    FailMission("تم تقييد اللاعب")
                    break
                end

                lastHealthCheck = currentTime
            end

            -- فحص انتهاء وقت المهمة كل 5 ثوانٍ
            if Najm.MissionFailure.checkTimeout and currentTime - lastTimeCheck >= 5000 then
                local elapsedTime = (currentTime - missionState.missionStartTime) / 1000 / 60

                if elapsedTime >= Najm.MissionDuration then
                    FailMission("انتهاء الوقت المحدد")
                    break
                end

                lastTimeCheck = currentTime
            end

            Wait(500) -- تقليل تكرار الاستطلاع لأداء أفضل
        end

        missionState.monitorThread = nil -- تنظيف مرجع الخيط
    end)
end

-- دالة التنظيف المحسنة مع إدارة الذاكرة المناسبة
CleanupMissionData = function()
    -- Stop all threads and timers
    missionState.monitoringActive = false
    missionState.showTimer = false
    missionState.missionStartTime = nil

    -- Clean up thread references
    if missionState.timerThread then
        missionState.timerThread = nil
    end
    if missionState.monitorThread then
        missionState.monitorThread = nil
    end

    -- Clean up delivery ped
    if missionState.deliveryPed and DoesEntityExist(missionState.deliveryPed) then
        DeleteEntity(missionState.deliveryPed)
        missionState.deliveryPed = nil
    end

    -- Optimized blip cleanup
    if missionState.deliveryBlips then
        for blipType, blip in pairs(missionState.deliveryBlips) do
            if blip and DoesBlipExist(blip) then
                RemoveBlip(blip)
            end
        end
        -- Clear the entire table efficiently
        for k in pairs(missionState.deliveryBlips) do
            missionState.deliveryBlips[k] = nil
        end
    end

    -- Remove mission bag
    DetachMissionBag()

    -- Reset all mission state variables
    missionState.currentMission = nil
    missionState.deliveryLocation = nil
    missionState.hasEnteredDeliveryZone = false

    -- Single server notification for cleanup
    TriggerServerEvent('BlackMarketMission:Server:CleanupMissionData')
end

-- Clean up on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        CleanupMissionData()
    end
end)

-- Clean up on player logout
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- Clean up any existing mission data when resource starts
        CleanupMissionData()
    end
end)

CreateThread(function()
    local model = Najm.NPC.model
    RequestModel(model)
    while not HasModelLoaded(model) do Wait(100) end
    local ped = CreatePed(0, model, Najm.NPC.coords.x, Najm.NPC.coords.y, Najm.NPC.coords.z, Najm.NPC.coords.w, false, true)
    FreezeEntityPosition(ped, true)
    SetEntityInvincible(ped, true)
    SetBlockingOfNonTemporaryEvents(ped, true)
    exports.ox_target:addLocalEntity(ped, {
        icon = 'fa-solid fa-briefcase',
        label = 'بدء مهمة السوق السوداء',
        onSelect = function ()
            TriggerServerEvent('BlackMarketMission:Server:StartMission')
        end
    })
end)

-- Function to handle the cut-scene sequence
function StartCutsceneSequence(playerPed, targetPed)
    -- Disable controls during the cut-scene
    SetPlayerControl(PlayerId(), false, 0)
    
    -- Play animations and handle camera
    PlayCutsceneAnimation(playerPed, targetPed)
    
    -- Wait for animations to complete
    Wait(5000)
    
    -- Complete the mission after the cut-scene
    TriggerServerEvent('BlackMarketMission:Server:CompleteMission')

    -- Note: Mission cleanup will be handled by server event 'CleanupMissionSuccess'

    -- Re-enable controls
    SetPlayerControl(PlayerId(), true, 0)
    ClearPedTasks(playerPed)
end

-- Function to play animations for the cut-scene
function PlayCutsceneAnimation(playerPed, targetPed)
    -- Create camera for the cutscene
    local cam = CreateCam("DEFAULT_SCRIPTED_CAMERA", true)
    
    -- Face entities towards each other
    TaskTurnPedToFaceEntity(playerPed, targetPed, 1000)
    TaskTurnPedToFaceEntity(targetPed, playerPed, 1000)
    Wait(1000)
    
    -- Get positions for camera setup
    local playerCoords = GetEntityCoords(playerPed)
    local targetCoords = GetEntityCoords(targetPed)
    
    -- Calculate midpoint between player and NPC
    local midPoint = vector3(
        (playerCoords.x + targetCoords.x) / 2,
        (playerCoords.y + targetCoords.y) / 2,
        (playerCoords.z + targetCoords.z) / 2 + 0.9
    )
    
    -- Calculate direction vector from player to NPC
    local dirVector = vector3(
        targetCoords.x - playerCoords.x,
        targetCoords.y - playerCoords.y,
        0.0
    )
    
    -- Normalize the direction vector
    local length = math.sqrt(dirVector.x * dirVector.x + dirVector.y * dirVector.y)
    dirVector = vector3(dirVector.x / length, dirVector.y / length, 0.0)
    
    -- Calculate perpendicular vector for side view
    local perpVector = vector3(-dirVector.y, dirVector.x, 0.0)
    
    -- Set initial camera position - side view
    local camPos = vector3(
        midPoint.x + perpVector.x * 2.5,
        midPoint.y + perpVector.y * 2.5,
        midPoint.z + 0.7
    )
    
    -- Set camera position and point at the midpoint
    SetCamCoord(cam, camPos.x, camPos.y, camPos.z)
    PointCamAtCoord(cam, midPoint.x, midPoint.y, midPoint.z)
    
    -- Activate camera with smooth transition
    SetCamActive(cam, true)
    RenderScriptCams(true, true, 800, true, true)
    
    -- Play give animation for player
    RequestAnimDict("mp_common")
    while not HasAnimDictLoaded("mp_common") do
        Wait(10)
    end
    
    -- First exchange - player gives item
    TaskPlayAnim(playerPed, "mp_common", "givetake1_a", 8.0, -8.0, 2000, 0, 0, false, false, false)
    Wait(500)
    
    -- Smoothly move camera during animation
    local startTime = GetGameTimer()
    local duration = 1500
    local startCamPos = vector3(camPos.x, camPos.y, camPos.z)
    local endCamPos = vector3(
        midPoint.x - perpVector.x * 2.5,
        midPoint.y - perpVector.y * 2.5,
        midPoint.z + 0.7
    )
    
    -- Smooth camera transition
    while GetGameTimer() - startTime < duration do
        local progress = (GetGameTimer() - startTime) / duration
        local currentPos = vector3(
            startCamPos.x + (endCamPos.x - startCamPos.x) * progress,
            startCamPos.y + (endCamPos.y - startCamPos.y) * progress,
            startCamPos.z + (endCamPos.z - startCamPos.z) * progress
        )
        SetCamCoord(cam, currentPos.x, currentPos.y, currentPos.z)
        PointCamAtCoord(cam, midPoint.x, midPoint.y, midPoint.z)
        Wait(0)
    end
    
    -- Play receive animation for NPC
    TaskPlayAnim(targetPed, "mp_common", "givetake1_b", 8.0, -8.0, 2000, 0, 0, false, false, false)
    Wait(2000)
    
    -- Second exchange - NPC gives item back
    local closeUpPos = vector3(
        midPoint.x + dirVector.x * 1.0,
        midPoint.y + dirVector.y * 1.0,
        midPoint.z + 1.2
    )
    
    -- Smooth transition to close-up
    startTime = GetGameTimer()
    duration = 1000
    startCamPos = vector3(endCamPos.x, endCamPos.y, endCamPos.z)
    endCamPos = closeUpPos
    
    while GetGameTimer() - startTime < duration do
        local progress = (GetGameTimer() - startTime) / duration
        local currentPos = vector3(
            startCamPos.x + (endCamPos.x - startCamPos.x) * progress,
            startCamPos.y + (endCamPos.y - startCamPos.y) * progress,
            startCamPos.z + (endCamPos.z - startCamPos.z) * progress
        )
        SetCamCoord(cam, currentPos.x, currentPos.y, currentPos.z)
        PointCamAtCoord(cam, targetCoords.x, targetCoords.y, targetCoords.z + 0.9)
        Wait(0)
    end
    
    -- Play animations for second exchange
    TaskPlayAnim(targetPed, "mp_common", "givetake1_a", 8.0, -8.0, 2000, 0, 0, false, false, false)
    Wait(1000)
    
    -- Play receive animation for player
    TaskPlayAnim(playerPed, "mp_common", "givetake1_b", 8.0, -8.0, 2000, 0, 0, false, false, false)
    Wait(2000)
    
    -- Return camera to normal with smooth transition
    RenderScriptCams(false, true, 1000, true, true)
    DestroyCam(cam, true)
    
    -- Clear animations
    ClearPedTasks(targetPed)
end

-- Optimized delivery NPC creation with efficient model loading
RegisterNetEvent('BlackMarketMission:Client:CreateDeliveryPed', function(x, y, z, w)
    -- Store current delivery location in optimized state
    missionState.deliveryLocation = vector3(x, y, z)

    local model = Najm.DeliveryNPCModel

    -- Optimized model loading with timeout
    RequestModel(model)
    local timeout = GetGameTimer() + 5000 -- 5 second timeout
    while not HasModelLoaded(model) and GetGameTimer() < timeout do
        Wait(50) -- Increased wait time to reduce CPU usage
    end

    if not HasModelLoaded(model) then
        if Najm.Debug then
            print('[DEBUG] Failed to load delivery NPC model: ' .. model)
        end
        return
    end

    missionState.deliveryPed = CreatePed(4, model, x, y, z, w, false, true)
    SetEntityAsMissionEntity(missionState.deliveryPed, true, true)
    SetBlockingOfNonTemporaryEvents(missionState.deliveryPed, true)

    -- Send the network ID to server for tracking (single event)
    local pedNetId = NetworkGetNetworkIdFromEntity(missionState.deliveryPed)
    TriggerServerEvent('BlackMarketMission:Server:SetDeliveryPedNetId', pedNetId)

    -- Add ox_target to the delivery NPC with cached reference
    exports.ox_target:addLocalEntity(missionState.deliveryPed, {
        icon = 'fa-solid fa-box',
        label = 'تسليم الحقيبة',
        onSelect = function()
            StartCutsceneSequence(PlayerPedId(), missionState.deliveryPed)
        end
    })

    -- Validate mission state efficiently
    if not missionState.currentMission then
        if Najm.Debug then
            print("^1ERROR: currentMission is nil in CreateDeliveryPed event")
        end
        return
    end

    -- Cache mission config to avoid repeated table lookups
    local Mission = Najm.Missions[missionState.currentMission]
    if not Mission then
        if Najm.Debug then
            print("^1ERROR: Mission '" .. missionState.currentMission .. "' not found in Najm.Missions")
        end
        return
    end

    -- Optimized blip creation with efficient cleanup
    if missionState.deliveryBlips then
        for _, blip in pairs(missionState.deliveryBlips) do
            if DoesBlipExist(blip) then
                RemoveBlip(blip)
            end
        end
    end

    -- Initialize blips table
    missionState.deliveryBlips = {}

    -- Create radius blip with cached coordinates
    local coords = missionState.deliveryLocation
    missionState.deliveryBlips.radiusBlip = AddBlipForRadius(coords.x, coords.y, coords.z, Mission.DilevryDist * 20.0)
    SetBlipColour(missionState.deliveryBlips.radiusBlip, 5)
    SetBlipAlpha(missionState.deliveryBlips.radiusBlip, 200)

    -- Create center marker blip
    missionState.deliveryBlips.centerBlip = AddBlipForCoord(coords.x, coords.y, coords.z)
    SetBlipSprite(missionState.deliveryBlips.centerBlip, 1)
    SetBlipColour(missionState.deliveryBlips.centerBlip, 5)
    SetBlipScale(missionState.deliveryBlips.centerBlip, 0.8)
    SetBlipAsShortRange(missionState.deliveryBlips.centerBlip, false)

    -- Set blip name efficiently
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("منطقة تسليم السوق السوداء")
    EndTextCommandSetBlipName(missionState.deliveryBlips.centerBlip)
end)

-- Event to set current mission
RegisterNetEvent('BlackMarketMission:Client:SetCurrentMission', function(missionId)
    missionState.currentMission = missionId
    -- Attach mission bag when mission starts
    AttachMissionBag()
    -- Start mission monitoring
    StartMissionMonitoring()
end)

-- Event to clean up mission
RegisterNetEvent('BlackMarketMission:Client:CleanupMission', function()
    CleanupMissionData()
end)

-- Event to handle mission failure from server
RegisterNetEvent('BlackMarketMission:Client:MissionFailed', function(reason)
    -- Stop mission monitoring and timer
    missionMonitoringActive = false
    showMissionTimer = false

    -- Send failure notification
    TriggerEvent("pNotify:SendNotification", {
        text = "<h1 style=\"color: #FF0000;\"><center>فشلت المهمة!</center></h1>" ..
               "<font color=\"white\" size=\"4\"><p align=\"right\"><b>السبب: " .. reason .. "</p>" ..
               "<font color=\"red\" size=\"4\"><p align=\"right\"><b>ثقتنا مو في محلها... ما نقدر نعتمد عليك!</p>" ..
               "<font color=\"yellow\" size=\"4\"><p align=\"right\"><b>حاول مرة ثانية بعد فترة وأثبت إنك تستاهل الثقة.</p>",
        type = "error",
        queue = "mission_failure",
        timeout = 10000,
        layout = "CenterLeft",
        killer = true
    })

    -- Clean up mission data
    CleanupMissionData()
end)

-- Event to clean up mission on success
RegisterNetEvent('BlackMarketMission:Client:CleanupMissionSuccess', function()
    -- Stop mission monitoring and timer immediately
    missionState.monitoringActive = false
    missionState.showTimer = false

    -- Remove all blips immediately
    if missionState.deliveryBlips then
        for _, blip in pairs(missionState.deliveryBlips) do
            if DoesBlipExist(blip) then
                RemoveBlip(blip)
            end
        end
        -- Clear the entire table efficiently
        for k in pairs(missionState.deliveryBlips) do
            missionState.deliveryBlips[k] = nil
        end
    end

    -- Remove mission bag
    DetachMissionBag()

    -- Reset mission variables
    missionState.currentMission = nil
    missionState.deliveryLocation = nil
    missionState.hasEnteredDeliveryZone = false

    if Najm.Debug then
        print('[DEBUG] Mission success cleanup completed - Blips removed, NPC will be deleted in 30 seconds')
    end
end)

-- Event to clean up mission on failure
RegisterNetEvent('BlackMarketMission:Client:CleanupMissionFailure', function(reason)
    -- Stop mission monitoring and timer immediately
    missionState.monitoringActive = false
    missionState.showTimer = false

    -- Remove all blips immediately
    if missionState.deliveryBlips then
        for _, blip in pairs(missionState.deliveryBlips) do
            if DoesBlipExist(blip) then
                RemoveBlip(blip)
            end
        end
        -- Clear the entire table efficiently
        for k in pairs(missionState.deliveryBlips) do
            missionState.deliveryBlips[k] = nil
        end
    end

    -- Remove mission bag
    DetachMissionBag()

    -- Reset mission variables
    missionState.currentMission = nil
    missionState.deliveryLocation = nil
    missionState.hasEnteredDeliveryZone = false

    if Najm.Debug then
        print('[DEBUG] Mission failure cleanup completed - Reason: ' .. reason .. ' - Blips removed, NPC will be deleted in 30 seconds')
    end
end)

-- Optimized delivery zone monitoring with reduced polling
CreateThread(function()
    while true do
        Wait(2000) -- Reduced frequency for better performance
        if missionState.currentMission and missionState.deliveryPed then
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local deliveryPedCoords = GetEntityCoords(missionState.deliveryPed)
            local dist = #(playerCoords - deliveryPedCoords)

            if dist < (Najm.Missions[missionState.currentMission].DilevryDist * 20.0) and not missionState.hasEnteredDeliveryZone then
                missionState.hasEnteredDeliveryZone = true
                TriggerEvent('ox_lib:notify', {
                    description = '[تنبيه] لقد دخلت منطقة التسليم!',
                    type = 'success',
                    position = 'top',
                    timeout = 5000
                })
            elseif dist >= (Najm.Missions[missionState.currentMission].DilevryDist * 20.0) and missionState.hasEnteredDeliveryZone then
                missionState.hasEnteredDeliveryZone = false
                TriggerEvent('ox_lib:notify', {
                    description = '[تنبيه] لقد غادرت منطقة التسليم!',
                    type = 'error',
                    position = 'top',
                    timeout = 5000
                })
            end
        else
            missionState.hasEnteredDeliveryZone = false
        end
    end
end)

RegisterNetEvent('BlackMarketMission:Client:SetWaypoint', function(coords)
    -- Waypoint functionality disabled for performance
    -- SetNewWaypoint(coords.x, coords.y)
    if Najm.Debug then
        print('[DEBUG] Waypoint request received for coords: ' .. tostring(coords))
    end
end)

RegisterNetEvent('BlackMarketMission:Client:DeleteDeliveryPed', function(pedNetId)
    if pedNetId then
        local ped = NetToPed(pedNetId)
        if DoesEntityExist(ped) then
            DeletePed(ped)
            if Najm.Debug then
                print('[DEBUG] Delivery NPC deleted via network ID: ' .. pedNetId)
            end
        else
            if Najm.Debug then
                print('[DEBUG] Could not find NPC with network ID: ' .. pedNetId)
            end
        end
    end

    -- Also delete local deliveryPed if it exists
    if missionState.deliveryPed and DoesEntityExist(missionState.deliveryPed) then
        DeletePed(missionState.deliveryPed)
        if Najm.Debug then
            print('[DEBUG] Local delivery NPC deleted')
        end
    end

    missionState.deliveryPed = nil
    missionState.hasEnteredDeliveryZone = false -- Reset the flag when mission ends
end)

