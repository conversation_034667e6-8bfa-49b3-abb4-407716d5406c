local currentMission = nil
local deliveryPed = nil
local deliveryBlip = nil
local deliveryBlips = nil
local hasEnteredDeliveryZone = false
local missionStartTime = nil
local missionMonitoringActive = false
local showMissionTimer = false

-- Function to attach mission bag to player (using clothing system)
local function AttachMissionBag()
    local playerPed = PlayerPedId()

    -- Set the mission bag clothing (bag from clothing store)
    SetPedComponentVariation(
        playerPed,
        Najm.MissionBag.componentId,  -- Component ID (5 = bags/parachute)
        Najm.MissionBag.drawableId,   -- Drawable ID (44 = bag from clothing store)
        Najm.MissionBag.textureId,    -- Texture ID
        0                             -- Palette ID
    )

    print('[DEBUG] Mission bag attached - Drawable: ' .. Najm.MissionBag.drawableId)
end

-- Function to detach mission bag from player (set to default 0, 0)
local function DetachMissionBag()
    local playerPed = PlayerPedId()

    -- Remove bag by setting to default values (0, 0)
    SetPedComponentVariation(
        playerPed,
        Najm.MissionBag.componentId,  -- Component ID (5 = bags/parachute)
        0,                            -- Default drawable (no bag)
        0,                            -- Default texture
        0                             -- Palette ID
    )

    print('[DEBUG] Mission bag removed - Set to default (0, 0)')
end

-- Function to format time for display
local function FormatTime(seconds)
    local minutes = math.floor(seconds / 60)
    local remainingSeconds = seconds % 60
    return string.format("%02d:%02d", minutes, remainingSeconds)
end

-- Function to display mission timer
local function DisplayMissionTimer()
    CreateThread(function()
        while showMissionTimer and currentMission do
            Wait(0)

            if missionStartTime then
                local currentTime = GetGameTimer()
                local elapsedTime = (currentTime - missionStartTime) / 1000 -- Convert to seconds
                local remainingTime = (Najm.MissionDuration * 60) - elapsedTime -- Total time in seconds

                if remainingTime > 0 then
                    local timeText = FormatTime(math.ceil(remainingTime))

                    -- Display timer on screen
                    SetTextFont(4)
                    SetTextProportional(1)
                    SetTextScale(0.8, 0.8)
                    SetTextColour(255, 255, 255, 255)
                    SetTextDropshadow(0, 0, 0, 0, 255)
                    SetTextEdge(1, 0, 0, 0, 255)
                    SetTextDropShadow()
                    SetTextOutline()
                    SetTextEntry("STRING")
                    AddTextComponentString("وقت المهمة المتبقي: " .. timeText)
                    DrawText(0.5, 0.05) -- Top center of screen

                    -- Draw background box
                    DrawRect(0.5, 0.05, 0.25, 0.05, 0, 0, 0, 100)
                else
                    -- Time is up, mission should fail
                    showMissionTimer = false
                    break
                end
            end
        end
    end)
end

-- Forward declaration
local CleanupMissionData

-- Function to handle mission failure
local function FailMission(reason)
    if not currentMission then return end

    -- Stop mission monitoring and timer
    missionMonitoringActive = false
    showMissionTimer = false

    -- Send failure notification with Arabic message
    TriggerEvent("pNotify:SendNotification", {
        text = "<h1 style=\"color: #FF0000;\"><center>فشلت المهمة!</center></h1>" ..
               "<font color=\"white\" size=\"4\"><p align=\"right\"><b>السبب: " .. reason .. "</p>" ..
               "<font color=\"red\" size=\"4\"><p align=\"right\"><b>ثقتنا مو في محلها... ما نقدر نعتمد عليك!</p>" ..
               "<font color=\"yellow\" size=\"4\"><p align=\"right\"><b>حاول مرة ثانية بعد فترة وأثبت إنك تستاهل الثقة.</p>",
        type = "error",
        queue = "mission_failure",
        timeout = 10000,
        layout = "CenterLeft",
        killer = true
    })

    -- Clean up mission data first
    CleanupMissionData()

    -- Notify server about mission failure
    TriggerServerEvent('BlackMarketMission:Server:FailMission', reason)
end

-- Function to start mission monitoring
local function StartMissionMonitoring()
    if missionMonitoringActive then return end

    missionMonitoringActive = true
    missionStartTime = GetGameTimer()
    showMissionTimer = true

    -- Start displaying the timer
    DisplayMissionTimer()

    CreateThread(function()
        while missionMonitoringActive and currentMission do
            Wait(1000) -- Check every second

            local playerPed = PlayerPedId()

            -- Check if player is dead
            if Najm.MissionFailure.checkDeath and IsEntityDead(playerPed) then
                FailMission("موت اللاعب")
                break
            end

            -- Check if player is handcuffed
            if Najm.MissionFailure.checkHandcuffed and IsPedCuffed(playerPed) then
                FailMission("تم تقييد اللاعب")
                break
            end

            -- Check mission timeout
            if Najm.MissionFailure.checkTimeout then
                local currentTime = GetGameTimer()
                local elapsedTime = (currentTime - missionStartTime) / 1000 / 60 -- Convert to minutes

                if elapsedTime >= Najm.MissionDuration then
                    FailMission("انتهاء الوقت المحدد")
                    break
                end
            end
        end
    end)
end

-- Function to clean up all mission data
CleanupMissionData = function()
    -- Stop mission monitoring and timer
    missionMonitoringActive = false
    showMissionTimer = false
    missionStartTime = nil

    if deliveryPed then
        DeleteEntity(deliveryPed)
        deliveryPed = nil
    end

    -- Remove all blips
    if deliveryBlips then
        if deliveryBlips.radiusBlip and DoesBlipExist(deliveryBlips.radiusBlip) then
            RemoveBlip(deliveryBlips.radiusBlip)
        end
        if deliveryBlips.centerBlip and DoesBlipExist(deliveryBlips.centerBlip) then
            RemoveBlip(deliveryBlips.centerBlip)
        end
        deliveryBlips = nil
    elseif deliveryBlip and DoesBlipExist(deliveryBlip) then
        RemoveBlip(deliveryBlip)
        deliveryBlip = nil
    end

    -- Remove mission bag
    DetachMissionBag()

    currentMission = nil
    hasEnteredDeliveryZone = false

    -- Notify server to clean up any database entries
    TriggerServerEvent('BlackMarketMission:Server:CleanupMissionData')
end

-- Clean up on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        CleanupMissionData()
    end
end)

-- Clean up on player logout
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- Clean up any existing mission data when resource starts
        CleanupMissionData()
    end
end)

CreateThread(function()
    local model = Najm.NPC.model
    RequestModel(model)
    while not HasModelLoaded(model) do Wait(100) end
    local ped = CreatePed(0, model, Najm.NPC.coords.x, Najm.NPC.coords.y, Najm.NPC.coords.z, Najm.NPC.coords.w, false, true)
    FreezeEntityPosition(ped, true)
    SetEntityInvincible(ped, true)
    SetBlockingOfNonTemporaryEvents(ped, true)
    exports.ox_target:addLocalEntity(ped, {
        icon = 'fa-solid fa-briefcase',
        label = 'بدء مهمة السوق السوداء',
        onSelect = function ()
            TriggerServerEvent('BlackMarketMission:Server:StartMission')
        end
    })
end)

-- Function to handle the cut-scene sequence
function StartCutsceneSequence(playerPed, targetPed)
    -- Disable controls during the cut-scene
    SetPlayerControl(PlayerId(), false, 0)
    
    -- Play animations and handle camera
    PlayCutsceneAnimation(playerPed, targetPed)
    
    -- Wait for animations to complete
    Wait(5000)
    
    -- Complete the mission after the cut-scene
    TriggerServerEvent('BlackMarketMission:Server:CompleteMission')

    -- Stop mission timer and remove mission bag when mission is completed
    showMissionTimer = false
    DetachMissionBag()

    -- Re-enable controls
    SetPlayerControl(PlayerId(), true, 0)
    ClearPedTasks(playerPed)
end

-- Function to play animations for the cut-scene
function PlayCutsceneAnimation(playerPed, targetPed)
    -- Create camera for the cutscene
    local cam = CreateCam("DEFAULT_SCRIPTED_CAMERA", true)
    
    -- Face entities towards each other
    TaskTurnPedToFaceEntity(playerPed, targetPed, 1000)
    TaskTurnPedToFaceEntity(targetPed, playerPed, 1000)
    Wait(1000)
    
    -- Get positions for camera setup
    local playerCoords = GetEntityCoords(playerPed)
    local targetCoords = GetEntityCoords(targetPed)
    
    -- Calculate midpoint between player and NPC
    local midPoint = vector3(
        (playerCoords.x + targetCoords.x) / 2,
        (playerCoords.y + targetCoords.y) / 2,
        (playerCoords.z + targetCoords.z) / 2 + 0.9
    )
    
    -- Calculate direction vector from player to NPC
    local dirVector = vector3(
        targetCoords.x - playerCoords.x,
        targetCoords.y - playerCoords.y,
        0.0
    )
    
    -- Normalize the direction vector
    local length = math.sqrt(dirVector.x * dirVector.x + dirVector.y * dirVector.y)
    dirVector = vector3(dirVector.x / length, dirVector.y / length, 0.0)
    
    -- Calculate perpendicular vector for side view
    local perpVector = vector3(-dirVector.y, dirVector.x, 0.0)
    
    -- Set initial camera position - side view
    local camPos = vector3(
        midPoint.x + perpVector.x * 2.5,
        midPoint.y + perpVector.y * 2.5,
        midPoint.z + 0.7
    )
    
    -- Set camera position and point at the midpoint
    SetCamCoord(cam, camPos.x, camPos.y, camPos.z)
    PointCamAtCoord(cam, midPoint.x, midPoint.y, midPoint.z)
    
    -- Activate camera with smooth transition
    SetCamActive(cam, true)
    RenderScriptCams(true, true, 800, true, true)
    
    -- Play give animation for player
    RequestAnimDict("mp_common")
    while not HasAnimDictLoaded("mp_common") do
        Wait(10)
    end
    
    -- First exchange - player gives item
    TaskPlayAnim(playerPed, "mp_common", "givetake1_a", 8.0, -8.0, 2000, 0, 0, false, false, false)
    Wait(500)
    
    -- Smoothly move camera during animation
    local startTime = GetGameTimer()
    local duration = 1500
    local startCamPos = vector3(camPos.x, camPos.y, camPos.z)
    local endCamPos = vector3(
        midPoint.x - perpVector.x * 2.5,
        midPoint.y - perpVector.y * 2.5,
        midPoint.z + 0.7
    )
    
    -- Smooth camera transition
    while GetGameTimer() - startTime < duration do
        local progress = (GetGameTimer() - startTime) / duration
        local currentPos = vector3(
            startCamPos.x + (endCamPos.x - startCamPos.x) * progress,
            startCamPos.y + (endCamPos.y - startCamPos.y) * progress,
            startCamPos.z + (endCamPos.z - startCamPos.z) * progress
        )
        SetCamCoord(cam, currentPos.x, currentPos.y, currentPos.z)
        PointCamAtCoord(cam, midPoint.x, midPoint.y, midPoint.z)
        Wait(0)
    end
    
    -- Play receive animation for NPC
    TaskPlayAnim(targetPed, "mp_common", "givetake1_b", 8.0, -8.0, 2000, 0, 0, false, false, false)
    Wait(2000)
    
    -- Second exchange - NPC gives item back
    local closeUpPos = vector3(
        midPoint.x + dirVector.x * 1.0,
        midPoint.y + dirVector.y * 1.0,
        midPoint.z + 1.2
    )
    
    -- Smooth transition to close-up
    startTime = GetGameTimer()
    duration = 1000
    startCamPos = vector3(endCamPos.x, endCamPos.y, endCamPos.z)
    endCamPos = closeUpPos
    
    while GetGameTimer() - startTime < duration do
        local progress = (GetGameTimer() - startTime) / duration
        local currentPos = vector3(
            startCamPos.x + (endCamPos.x - startCamPos.x) * progress,
            startCamPos.y + (endCamPos.y - startCamPos.y) * progress,
            startCamPos.z + (endCamPos.z - startCamPos.z) * progress
        )
        SetCamCoord(cam, currentPos.x, currentPos.y, currentPos.z)
        PointCamAtCoord(cam, targetCoords.x, targetCoords.y, targetCoords.z + 0.9)
        Wait(0)
    end
    
    -- Play animations for second exchange
    TaskPlayAnim(targetPed, "mp_common", "givetake1_a", 8.0, -8.0, 2000, 0, 0, false, false, false)
    Wait(1000)
    
    -- Play receive animation for player
    TaskPlayAnim(playerPed, "mp_common", "givetake1_b", 8.0, -8.0, 2000, 0, 0, false, false, false)
    Wait(2000)
    
    -- Return camera to normal with smooth transition
    RenderScriptCams(false, true, 1000, true, true)
    DestroyCam(cam, true)
    
    -- Clear animations
    ClearPedTasks(targetPed)
end

-- Event to create delivery NPC
RegisterNetEvent('BlackMarketMission:Client:CreateDeliveryPed', function(x, y, z, w)
    local model = Najm.DeliveryNPCModel
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(10)
    end

    deliveryPed = CreatePed(4, model, x, y, z, w, false, true)
    SetEntityAsMissionEntity(deliveryPed, true, true)
    SetBlockingOfNonTemporaryEvents(deliveryPed, true)

    -- Add ox_target to the delivery NPC
    exports.ox_target:addLocalEntity(deliveryPed, {
        icon = 'fa-solid fa-box',
        label = 'تسليم الحقيبة',
        onSelect = function()
            StartCutsceneSequence(PlayerPedId(), deliveryPed)
        end
    })

    -- Check if currentMission is valid
    if not currentMission then
        print("^1ERROR: currentMission is nil in CreateDeliveryPed event")
        return
    end

    -- Check if mission exists in config
    local Mission = Najm.Missions[currentMission]
    if not Mission then
        print("^1ERROR: Mission '" .. currentMission .. "' not found in Najm.Missions")
        return
    end

    -- Create a blip for the delivery area
    -- Remove old blip if exists
    if deliveryBlip then
        RemoveBlip(deliveryBlip)
    end
    
    -- Create a more visible radius blip
    deliveryBlip = AddBlipForRadius(Mission.DilevryPoint.x, Mission.DilevryPoint.y, Mission.DilevryPoint.z, Mission.DilevryDist * 20.0)
    SetBlipColour(deliveryBlip, 5) -- Yellow color (5)
    SetBlipAlpha(deliveryBlip, 200) -- Increased alpha for better visibility
    
    -- Add a center marker blip for better visibility
    local centerBlip = AddBlipForCoord(Mission.DilevryPoint.x, Mission.DilevryPoint.y, Mission.DilevryPoint.z)
    SetBlipSprite(centerBlip, 1) -- Standard blip
    SetBlipColour(centerBlip, 5) -- Yellow color (5)
    SetBlipScale(centerBlip, 0.8) -- Slightly smaller
    SetBlipAsShortRange(centerBlip, false) -- Visible from far away
    
    -- Set blip name
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("منطقة تسليم السوق السوداء")
    EndTextCommandSetBlipName(centerBlip)
    
    -- Store both blips in a table
    if not deliveryBlips then deliveryBlips = {} end
    deliveryBlips = {radiusBlip = deliveryBlip, centerBlip = centerBlip}
    deliveryBlip = deliveryBlips.radiusBlip -- Keep compatibility with existing code
end)

-- Event to set current mission
RegisterNetEvent('BlackMarketMission:Client:SetCurrentMission', function(missionId)
    currentMission = missionId
    -- Attach mission bag when mission starts
    AttachMissionBag()
    -- Start mission monitoring
    StartMissionMonitoring()
end)

-- Event to clean up mission
RegisterNetEvent('BlackMarketMission:Client:CleanupMission', function()
    CleanupMissionData()
end)

-- Event to handle mission failure from server
RegisterNetEvent('BlackMarketMission:Client:MissionFailed', function(reason)
    -- Stop mission monitoring and timer
    missionMonitoringActive = false
    showMissionTimer = false

    -- Send failure notification
    TriggerEvent("pNotify:SendNotification", {
        text = "<h1 style=\"color: #FF0000;\"><center>فشلت المهمة!</center></h1>" ..
               "<font color=\"white\" size=\"4\"><p align=\"right\"><b>السبب: " .. reason .. "</p>" ..
               "<font color=\"red\" size=\"4\"><p align=\"right\"><b>ثقتنا مو في محلها... ما نقدر نعتمد عليك!</p>" ..
               "<font color=\"yellow\" size=\"4\"><p align=\"right\"><b>حاول مرة ثانية بعد فترة وأثبت إنك تستاهل الثقة.</p>",
        type = "error",
        queue = "mission_failure",
        timeout = 10000,
        layout = "CenterLeft",
        killer = true
    })

    -- Clean up mission data
    CleanupMissionData()
end)

-- Thread to check delivery zone
CreateThread(function()
    while true do
        Wait(1000)
        if currentMission and deliveryPed then
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local deliveryPedCoords = GetEntityCoords(deliveryPed)
            local dist = #(playerCoords - deliveryPedCoords)
            
            if dist < (Najm.Missions[currentMission].DilevryDist * 20.0) and not hasEnteredDeliveryZone then
                hasEnteredDeliveryZone = true
                TriggerEvent('ox_lib:notify', {
                    description = '[تنبيه] لقد دخلت منطقة التسليم!',
                    type = 'success',
                    position = 'top',
                    timeout = 5000
                })
            elseif dist >= (Najm.Missions[currentMission].DilevryDist * 20.0) and hasEnteredDeliveryZone then
                hasEnteredDeliveryZone = false
                TriggerEvent('ox_lib:notify', {
                    description = '[تنبيه] لقد غادرت منطقة التسليم!',
                    type = 'error',
                    position = 'top',
                    timeout = 5000
                })
            end
        else
            hasEnteredDeliveryZone = false
        end
    end
end)

RegisterNetEvent('BlackMarketMission:Client:SetWaypoint', function(coords)
    -- SetNewWaypoint(coords.x, coords.y) -- Removed waypoint setting
    -- TriggerEvent('chatMessage', '', {255, 255, 0}, '[السوق السوداء] تم تحديد موقع التسليم على الخريطة.') -- Keeping chat message
end)

RegisterNetEvent('BlackMarketMission:Client:DeleteDeliveryPed', function(pedNetId)
    local ped = NetToPed(pedNetId)
    if DoesEntityExist(ped) then
        DeletePed(ped)
    end
    
    -- Remove all blips
    if deliveryBlips then
        if deliveryBlips.radiusBlip and DoesBlipExist(deliveryBlips.radiusBlip) then
            RemoveBlip(deliveryBlips.radiusBlip)
        end
        if deliveryBlips.centerBlip and DoesBlipExist(deliveryBlips.centerBlip) then
            RemoveBlip(deliveryBlips.centerBlip)
        end
        deliveryBlips = nil
    elseif DoesBlipExist(deliveryBlip) then
        RemoveBlip(deliveryBlip)
    end
    
    deliveryPed = nil
    deliveryBlip = nil
    hasEnteredDeliveryZone = false -- Reset the flag when mission ends
end)

