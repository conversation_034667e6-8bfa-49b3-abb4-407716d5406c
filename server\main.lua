local deliveryPedNetId = nil -- Global to store the network ID of the delivery NPC

local function getMissionState(cb)
    MySQL.query('SELECT is_active, last_time FROM blackmarket_mission WHERE id = 1', {}, function(result)
        if result and result[1] then
            cb(result[1].is_active == 1, result[1].last_time)
        else
            -- If no record exists, create one
            MySQL.insert('INSERT INTO blackmarket_mission (id, is_active, last_time) VALUES (1, 0, 0)', {}, function()
                cb(false, 0)
            end)
        end
    end)
end

function setMissionState(isActive, lastTime)
    MySQL.update('UPDATE blackmarket_mission SET is_active = ?, last_time = ? WHERE id = 1', {
        isActive and 1 or 0, lastTime
    }, function(affectedRows)
        if affectedRows > 0 then
            print('[DEBUG] Successfully updated mission state: active=' .. tostring(isActive) .. ', time=' .. lastTime)
            if not isActive and deliveryPedNetId then
                TriggerClientEvent('BlackMarketMission:Client:DeleteDeliveryPed', -1, deliveryPedNetId)
                deliveryPedNetId = nil
            end
        else
            print('[ERROR] Failed to update mission state')
        end
    end)
end

local isMissionStarting = false

RegisterNetEvent('BlackMarketMission:Server:StartMission', function()
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    if not xPlayer then return end

    if isMissionStarting then
        TriggerClientEvent('ox_lib:notify', src, {
            description = 'جاري بدء المهمة... حاول بعد لحظة.',
            type = 'error',
            position = 'top'
        })
        return
    end
    
    isMissionStarting = true

    getMissionState(function(isActive, lastTime)
        local now = os.time()
        if isActive then
            isMissionStarting = false
            TriggerClientEvent('ox_lib:notify', src, {
                description = 'تعال لي بكرة مشغول شوي',
                type = 'error',
                position = 'top'
            })
            return
        end

        if now - lastTime < Najm.MissionCooldown then
            isMissionStarting = false
            local remaining = math.floor((Najm.MissionCooldown - (now - lastTime)) / 60)
            TriggerClientEvent('ox_lib:notify', src, {
                description = 'مشغول ذحين تعال بعد ' .. remaining .. ' دقيقة',
                type = 'error',
                position = 'top'
            })
            return
        end

        -- Give mission items first
        local currentMission = "MissionTHeBefor"
        local missionItem = Najm.Missions[currentMission].MissionItem
        xPlayer.addInventoryItem(missionItem, 1)

        -- Set current mission on client side
        TriggerClientEvent('BlackMarketMission:Client:SetCurrentMission', src, currentMission)

        -- If item was added successfully, set mission state
        setMissionState(true, now)
        isMissionStarting = false

        TriggerClientEvent('ox_lib:notify', src, {
            description = 'بدات المهمة وعطيتك الشنطة ورني شغلك يا ذيبان',
            type = 'success',
            position = 'top'
        })

        -- Notification for mission instructions using pNotify with Saudi slang and colors
        TriggerClientEvent("pNotify:SendNotification", src, {
            text = "<h1 style=\"color: #FFD700;\"><center>يا ذيبان! قدّامك اختبار رجال</center></h1>"..
                   "<font color=\"white\" size=\"4\"><p align=\"right\"><b>أمامك <font color=\"#00FF00\">" .. Najm.MissionDuration .. " دقايق</font> توصل الحقيبة للمكان المطلوب.</p>"..
                   "<font color=\"white\" size=\"4\"><p align=\"right\"><b>إذا خلصتها صح؟ توصلك بطاقة السوق السوداء.. وقتها تصير من أهل اللعبة.</p>"..
                   "<font color=\"white\" size=\"4\"><p align=\"right\"><b>اضغط <font color=\"#00BFFF\">[G]</font> عشان تشوف نقطة التسليم.</p>"..
                   "<font color=\"red\" size=\"5\"><p align=\"right\"><b>خلك صاحي.. الشرطة متحفزين لأي غلطة، لا تخربها!</p>",
            type = "error",
            queue = "left",
            timeout = 15000,
            layout = "CenterLeft",
            killer = true
        })

        -- Police Notification
        local onlinePlayers = ESX.GetPlayers()
        for i=1, #onlinePlayers do
            local xPlayer = ESX.GetPlayerFromId(onlinePlayers[i])
            if xPlayer.job.name == Najm.PoliceJob then
                TriggerClientEvent("pNotify:SendNotification", xPlayer.source, {
                    text = "<h1 style=\"color: #FF0000;\"><center>تنبيه عاجل!</center></h1>" ..
                           "<font color=\"white\" size=\"4\"><p align=\"right\"><b>بلاغ وارد من مصادر غير معروفة حول تحرّك غريب في إحدى المناطق المشبوهة.</p>" ..
                           "<font color=\"yellow\" size=\"4\"><p align=\"right\"><b>الوضع غير واضح حتى الآن، لكن هناك مؤشرات على نشاط غير قانوني.</p>" ..
                           "<font color=\"white\" size=\"4\"><p align=\"right\"><b>الرجاء التوجه فورًا للموقع والتحقّق من الأمر.</p>",
                    type = "warning",
                    queue = "police_alerts",
                    timeout = 30000,
                    layout = "CenterRight",
                    killer = true
                })
            end
        end

        -- Trigger client event to create the delivery NPC
        local missionCoords = Najm.Missions[currentMission].DilevryPoint
        TriggerClientEvent('BlackMarketMission:Client:CreateDeliveryPed', -1, missionCoords.x, missionCoords.y, missionCoords.z, 0.0) -- Pass 0.0 as heading, as DilevryPoint is vector3

        -- Set timeout to end mission
        SetTimeout(Najm.MissionDuration * 60 * 1000, function()
            setMissionState(false, os.time())
        end)
    end)
end)

RegisterNetEvent('BlackMarketMission:Server:LocateMission', function()
    local src = source
    local currentMission = "MissionTHeBefor"
    TriggerClientEvent('BlackMarketMission:Client:SetWaypoint', src, Najm.Missions[currentMission].DilevryPoint)
    TriggerClientEvent('ox_lib:notify', src, {
        description = 'تم تحديد موقع التسليم على الخريطة.',
        type = 'info',
        position = 'top'
    })
end)

RegisterNetEvent('BlackMarketMission:Server:CompleteMission', function()
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    local currentMission = "MissionTHeBefor"
    local missionItem = Najm.Missions[currentMission].MissionItem
    local rewardItem = Najm.Missions[currentMission].RewardItem

    if xPlayer.getInventoryItem(missionItem).count >= 1 then
        xPlayer.removeInventoryItem(missionItem, 1)
        xPlayer.addInventoryItem(rewardItem, 1)
        setMissionState(false, os.time()) -- Set mission to inactive after completion

        TriggerClientEvent('ox_lib:notify', src, {
            description = 'تم تسليم الحقيبة بنجاح! لقد حصلت على ' .. ESX.GetItemLabel(rewardItem) .. '.',
            type = 'success',
            position = 'top'
        })
    else
        TriggerClientEvent('ox_lib:notify', src, {
            description = 'لا تمتلك الحقيبة لتسليمها.',
            type = 'error',
            position = 'top'
        })
    end
end)

-- Event to clean up mission data from database
RegisterNetEvent('BlackMarketMission:Server:CleanupMissionData', function()
    -- Mission cleanup is handled by the global mission state
    -- No individual player tracking needed since missions are server-wide
    print('[DEBUG] Mission cleanup requested by player: ' .. source)
end)

-- Reset all mission data on resource start
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        MySQL.query('UPDATE black_market_missions SET current_mission = 0, mission_completed = 0, mission_started = 0')
    end
end)