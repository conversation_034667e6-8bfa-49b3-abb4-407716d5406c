-- إدارة حالة الخادم المحسنة
local serverState = {
    deliveryPedNetId = nil,
    currentDeliveryLocation = nil,
    lastPoliceCheck = 0,
    cachedPoliceCount = 0,
    activeMissions = {},
    missionCooldowns = {}
}

-- اختيار الموقع العشوائي المحسن مع التحقق
local function SelectRandomDeliveryLocation()
    local locations = Najm.DeliveryLocations
    if not locations or #locations == 0 then
        if Najm.Debug then
            print('[تشخيص] لا توجد مواقع تسليم مكونة')
        end
        return nil
    end

    local randomIndex = math.random(1, #locations)
    serverState.currentDeliveryLocation = locations[randomIndex]
    return serverState.currentDeliveryLocation
end

-- عدد الشرطة المحسن مع التخزين المؤقت لتقليل استدعاءات ESX
local function getOnlinePoliceCount()
    local currentTime = os.time()

    -- تخزين عدد الشرطة مؤقتاً لمدة 30 ثانية لتقليل استدعاءات قاعدة البيانات/ESX
    if currentTime - serverState.lastPoliceCheck < 30 then
        return serverState.cachedPoliceCount
    end

    local policeCount = 0
    local onlinePlayers = ESX.GetPlayers()

    -- حلقة محسنة مع إمكانية الخروج المبكر
    for i = 1, #onlinePlayers do
        local xPlayer = ESX.GetPlayerFromId(onlinePlayers[i])
        if xPlayer and xPlayer.job and xPlayer.job.name == Najm.PoliceJob then
            policeCount = policeCount + 1
        end
    end

    -- تحديث التخزين المؤقت
    serverState.cachedPoliceCount = policeCount
    serverState.lastPoliceCheck = currentTime

    return policeCount
end

local function getMissionState(cb)
    MySQL.query('SELECT is_active, last_time FROM blackmarket_mission WHERE id = 1', {}, function(result)
        if result and result[1] then
            cb(result[1].is_active == 1, result[1].last_time)
        else
            -- If no record exists, create one
            MySQL.insert('INSERT INTO blackmarket_mission (id, is_active, last_time) VALUES (1, 0, 0)', {}, function()
                cb(false, 0)
            end)
        end
    end)
end

function setMissionState(isActive, lastTime)
    MySQL.update('UPDATE blackmarket_mission SET is_active = ?, last_time = ? WHERE id = 1', {
        isActive and 1 or 0, lastTime
    }, function(affectedRows)
        if affectedRows > 0 then
            if Najm.Debug then
                print('[DEBUG] Successfully updated mission state: active=' .. tostring(isActive) .. ', time=' .. lastTime)
            end
            if not isActive and serverState.deliveryPedNetId then
                TriggerClientEvent('BlackMarketMission:Client:DeleteDeliveryPed', -1, serverState.deliveryPedNetId)
                serverState.deliveryPedNetId = nil
            end
        else
            if Najm.Debug then
                print('[ERROR] Failed to update mission state')
            end
        end
    end)
end

local isMissionStarting = false

RegisterNetEvent('BlackMarketMission:Server:StartMission', function()
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    if not xPlayer then return end

    if isMissionStarting then
        TriggerClientEvent('ox_lib:notify', src, {
            description = 'جاري بدء المهمة... حاول بعد لحظة.',
            type = 'error',
            position = 'top'
        })
        return
    end
    
    isMissionStarting = true

    -- Check if there are enough police officers online
    local policeCount = getOnlinePoliceCount()
    if policeCount < Najm.MinCops then
        isMissionStarting = false
        TriggerClientEvent('ox_lib:notify', src, {
            description = 'مافي شرطة كافيين متصلين الحين، تعال بعدين! (مطلوب: ' .. Najm.MinCops .. ' - متصل: ' .. policeCount .. ')',
            type = 'error',
            position = 'top',
            timeout = 7000
        })
        return
    end

    getMissionState(function(isActive, lastTime)
        local now = os.time()
        if isActive then
            isMissionStarting = false
            TriggerClientEvent('ox_lib:notify', src, {
                description = 'تعال لي بكرة مشغول شوي',
                type = 'error',
                position = 'top'
            })
            return
        end

        if now - lastTime < Najm.MissionCooldown then
            isMissionStarting = false
            local remaining = math.floor((Najm.MissionCooldown - (now - lastTime)) / 60)
            TriggerClientEvent('ox_lib:notify', src, {
                description = 'مشغول ذحين تعال بعد ' .. remaining .. ' دقيقة',
                type = 'error',
                position = 'top'
            })
            return
        end

        -- Select random delivery location
        SelectRandomDeliveryLocation()

        -- Give mission items first
        local currentMission = "MissionTHeBefor"
        local missionItem = Najm.Missions[currentMission].MissionItem
        xPlayer.addInventoryItem(missionItem, 1)

        -- Set current mission on client side
        TriggerClientEvent('BlackMarketMission:Client:SetCurrentMission', src, currentMission)

        -- If item was added successfully, set mission state
        setMissionState(true, now)
        isMissionStarting = false

        TriggerClientEvent('ox_lib:notify', src, {
            description = 'بدات المهمة وعطيتك الشنطة ورني شغلك يا ذيبان',
            type = 'success',
            position = 'top'
        })

        -- Notification for mission instructions using pNotify with Saudi slang and colors
        TriggerClientEvent("pNotify:SendNotification", src, {
            text = "<h1 style=\"color: #FFD700;\"><center>يا ذيبان! قدّامك اختبار رجال</center></h1>"..
                   "<font color=\"white\" size=\"4\"><p align=\"right\"><b>أمامك <font color=\"#00FF00\">" .. Najm.MissionDuration .. " دقايق</font> توصل الحقيبة للمكان المطلوب.</p>"..
                   "<font color=\"white\" size=\"4\"><p align=\"right\"><b>إذا خلصتها صح؟ توصلك بطاقة السوق السوداء.. وقتها تصير من أهل اللعبة.</p>"..
                   "<font color=\"white\" size=\"4\"><p align=\"right\"><b>اضغط <font color=\"#00BFFF\">[G]</font> عشان تشوف نقطة التسليم.</p>"..
                   "<font color=\"red\" size=\"5\"><p align=\"right\"><b>خلك صاحي.. الشرطة متحفزين لأي غلطة، لا تخربها!</p>",
            type = "error",
            queue = "left",
            timeout = 15000,
            layout = "CenterLeft",
            killer = true
        })

        -- Police Notification
        local onlinePlayers = ESX.GetPlayers()
        for i=1, #onlinePlayers do
            local xPlayer = ESX.GetPlayerFromId(onlinePlayers[i])
            if xPlayer.job.name == Najm.PoliceJob then
                TriggerClientEvent("pNotify:SendNotification", xPlayer.source, {
                    text = "<h1 style=\"color: #FF0000;\"><center>تنبيه عاجل!</center></h1>" ..
                           "<font color=\"white\" size=\"4\"><p align=\"right\"><b>بلاغ وارد من مصادر غير معروفة حول تحرّك غريب في إحدى المناطق المشبوهة.</p>" ..
                           "<font color=\"yellow\" size=\"4\"><p align=\"right\"><b>الوضع غير واضح حتى الآن، لكن هناك مؤشرات على نشاط غير قانوني.</p>" ..
                           "<font color=\"white\" size=\"4\"><p align=\"right\"><b>الرجاء التوجه فورًا للموقع والتحقّق من الأمر.</p>",
                    type = "warning",
                    queue = "police_alerts",
                    timeout = 30000,
                    layout = "CenterRight",
                    killer = true
                })
            end
        end

        -- Trigger client event to create the delivery NPC at random location
        TriggerClientEvent('BlackMarketMission:Client:CreateDeliveryPed', -1, serverState.currentDeliveryLocation.coords.x, serverState.currentDeliveryLocation.coords.y, serverState.currentDeliveryLocation.coords.z, serverState.currentDeliveryLocation.heading)

        -- Set timeout to end mission
        SetTimeout(Najm.MissionDuration * 60 * 1000, function()
            setMissionState(false, os.time())
        end)
    end)
end)

-- حدث لتعيين معرف شبكة NPC التسليم
RegisterNetEvent('BlackMarketMission:Server:SetDeliveryPedNetId', function(pedNetId)
    serverState.deliveryPedNetId = pedNetId
    if Najm.Debug then
        print('[تشخيص] تم تعيين معرف شبكة NPC التسليم: ' .. pedNetId)
    end
end)

RegisterNetEvent('BlackMarketMission:Server:LocateMission', function()
    local src = source
    if serverState.currentDeliveryLocation then
        TriggerClientEvent('BlackMarketMission:Client:SetWaypoint', src, serverState.currentDeliveryLocation.coords)
        TriggerClientEvent('ox_lib:notify', src, {
            description = 'تم تحديد موقع التسليم على الخريطة.',
            type = 'info',
            position = 'top'
        })
    else
        TriggerClientEvent('ox_lib:notify', src, {
            description = 'لا توجد مهمة نشطة حالياً.',
            type = 'error',
            position = 'top'
        })
    end
end)

RegisterNetEvent('BlackMarketMission:Server:CompleteMission', function()
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    local currentMission = "MissionTHeBefor"
    local missionItem = Najm.Missions[currentMission].MissionItem
    local rewardItem = Najm.Missions[currentMission].RewardItem

    if xPlayer.getInventoryItem(missionItem).count >= 1 then
        xPlayer.removeInventoryItem(missionItem, 1)
        xPlayer.addInventoryItem(rewardItem, 1)
        setMissionState(false, os.time()) -- Set mission to inactive after completion

        TriggerClientEvent('ox_lib:notify', src, {
            description = 'تم تسليم الحقيبة بنجاح! لقد حصلت على ' .. ESX.GetItemLabel(rewardItem) .. '.',
            type = 'success',
            position = 'top'
        })

        -- Clean up blips immediately and schedule NPC deletion
        TriggerClientEvent('BlackMarketMission:Client:CleanupMissionSuccess', -1)

        -- حذف NPC التسليم بعد 30 ثانية
        if serverState.deliveryPedNetId then
            if Najm.Debug then
                print('[تشخيص] جدولة حذف NPC خلال 30 ثانية - معرف الشبكة: ' .. serverState.deliveryPedNetId)
            end
            SetTimeout(30000, function()
                if Najm.Debug then
                    print('[تشخيص] حذف NPC الآن - معرف الشبكة: ' .. serverState.deliveryPedNetId)
                end
                TriggerClientEvent('BlackMarketMission:Client:DeleteDeliveryPed', -1, serverState.deliveryPedNetId)
                serverState.deliveryPedNetId = nil
            end)
        else
            if Najm.Debug then
                print('[تشخيص] لم يتم العثور على معرف شبكة NPC التسليم للحذف')
            end
        end
    else
        TriggerClientEvent('ox_lib:notify', src, {
            description = 'لا تمتلك الحقيبة لتسليمها.',
            type = 'error',
            position = 'top'
        })
    end
end)

-- حدث للتعامل مع فشل المهمة
RegisterNetEvent('BlackMarketMission:Server:FailMission', function(reason)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    local currentMission = "MissionTHeBefor"
    local missionItem = Najm.Missions[currentMission].MissionItem

    -- إزالة عنصر المهمة إذا كان اللاعب يملكه
    if xPlayer.getInventoryItem(missionItem).count >= 1 then
        xPlayer.removeInventoryItem(missionItem, 1)
    end

    -- تعيين المهمة كغير نشطة وإعادة تعيين فترة الانتظار (السماح بإعادة المحاولة فوراً بعد الفشل)
    setMissionState(false, 0)

    -- إجبار تنظيف قاعدة البيانات - إعادة تعيين is_active و last_time
    MySQL.update('UPDATE blackmarket_mission SET is_active = 0, last_time = 0 WHERE id = 1', {}, function(affectedRows)
        if Najm.Debug then
            if affectedRows > 0 then
                print('[تشخيص] فشلت المهمة - تم تنظيف قاعدة البيانات للاعب ' .. src .. ' - السبب: ' .. reason)
            else
                print('[تشخيص] فشلت المهمة ولكن لم يتم تحديث سجلات قاعدة البيانات للاعب ' .. src .. ' - السبب: ' .. reason)
            end
        end
    end)

    -- تنظيف العلامات فوراً وجدولة حذف NPC
    TriggerClientEvent('BlackMarketMission:Client:CleanupMissionFailure', -1, reason)

    -- حذف NPC التسليم بعد 30 ثانية
    if serverState.deliveryPedNetId then
        if Najm.Debug then
            print('[تشخيص] جدولة حذف NPC خلال 30 ثانية (فشل) - معرف الشبكة: ' .. serverState.deliveryPedNetId)
        end
        SetTimeout(30000, function()
            if Najm.Debug then
                print('[تشخيص] حذف NPC الآن (فشل) - معرف الشبكة: ' .. serverState.deliveryPedNetId)
            end
            TriggerClientEvent('BlackMarketMission:Client:DeleteDeliveryPed', -1, serverState.deliveryPedNetId)
            serverState.deliveryPedNetId = nil
        end)
    else
        if Najm.Debug then
            print('[تشخيص] لم يتم العثور على معرف شبكة NPC التسليم للحذف (فشل)')
        end
    end
end)

-- حدث لتنظيف بيانات المهمة من قاعدة البيانات
RegisterNetEvent('BlackMarketMission:Server:CleanupMissionData', function()
    -- يتم التعامل مع تنظيف المهمة بواسطة حالة المهمة العامة
    -- لا حاجة لتتبع اللاعبين الفرديين لأن المهام على مستوى الخادم
    serverState.deliveryPedNetId = nil
    serverState.currentDeliveryLocation = nil
    if Najm.Debug then
        print('[تشخيص] طلب تنظيف المهمة من اللاعب: ' .. source)
    end
end)

-- إعادة تعيين جميع بيانات المهمة عند بدء المورد
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- إعادة تعيين حالة المهمة العامة لغير نشطة وتنظيف قاعدة البيانات
        setMissionState(false, 0)

        -- تنظيف قاعدة البيانات عند إعادة التشغيل
        MySQL.update('UPDATE blackmarket_mission SET is_active = 0, last_time = 0 WHERE id = 1', {}, function(affectedRows)
            if Najm.Debug then
                if affectedRows > 0 then
                    print('[تشخيص] تم تنظيف قاعدة البيانات عند بدء المورد - تم إعادة تعيين حالة المهمة')
                else
                    print('[تشخيص] لا توجد سجلات قاعدة بيانات للتنظيف عند بدء المورد')
                end
            end
        end)

        if Najm.Debug then
            print('[تشخيص] تم بدء المورد - تم تنظيف حالة المهمة وقاعدة البيانات')
        end
    end
end)