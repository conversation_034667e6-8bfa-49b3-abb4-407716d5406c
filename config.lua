Najm = {}

Najm.NPC = {
    coords = vector4(1921.180, 3728.293, 31.783, 22.26),        -- مكان بدأ المهمة 
    model = 'a_m_m_og_boss_01'                                  -- موديل الشخصية 
}

Najm.MissionCooldown = 60 * 60 * 2                              -- الوقت الذي ينتظره اللاعب بين المهمة | ساعتين  

Najm.MissionDuration = 10                                       -- مدة المهمة |10 دقائق 

Najm.DeliveryNPCModel = 's_m_y_dealer_01'                        -- موديل شخصية التسليم في نهاية المهمة

Najm.PoliceJob = 'police'

-- Mission bag/briefcase configuration (using clothing system)
Najm.MissionBag = {
    componentId = 5,    -- Parachute/Bag component slot
    drawableId = 44,    -- Bag drawable ID from clothing store
    textureId = 0       -- Texture variation (can be changed if needed)
}

Najm.Missions = {
    MissionTHeBefor = {
        MissionItem = 'water',                       -- اسم الايتم اللي يجب يوصله اللاعب 
        RewardItem = 'bread',                       -- اسم الايتم اللي بيدخله السوق واللي هي بطاقة السوق السوداء
        MarketLocater = 'iron',                       -- اسم الايتم الذي يحصل عليه اللاعب وهو اللي بيحدد مكان السوق السوداء
        DilevryPoint = vector3(-91.1408, 4586.104, 123.15 -1),    -- االمكان اللي لازم يوصل له اللاعب  الشنطة 
        DilevryDist = 3.0,                                      -- المسافة بين اللاعب و  DilevryPoint عشان يوصلها 
        DilevryKey = 38,                                        -- الزر اللي يضغطه اللاعب لما  يكون عند   DilevryPoint  عشان يوصلها 
    },
    MissonOneTest = {

    },
    MissionTwoTest = {

    }
}

