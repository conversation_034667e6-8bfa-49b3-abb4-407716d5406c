Najm = {}

Najm.Debug = true                                           -- تشغيل/إيقاف رسائل التشخيص (Debug Messages)

Najm.NPC = {
    coords = vector4(1921.180, 3728.293, 31.783, 22.26),        -- مكان بدأ المهمة 
    model = 'a_m_m_og_boss_01'                                  -- موديل الشخصية 
}

Najm.MissionCooldown = 60 * 60 * 2                              -- الوقت الذي ينتظره اللاعب بين المهمة | ساعتين  

Najm.MissionDuration = 10                                       -- مدة المهمة |10 دقائق

Najm.DeliveryNPCModel = 's_m_y_dealer_01'                        -- موديل شخصية التسليم في نهاية المهمة

-- Mission failure conditions
Najm.MissionFailure = {
    checkDeath = true,          -- فحص موت اللاعب
    checkHandcuffed = true,     -- فحص تكبيل اللاعب
    checkTimeout = true         -- فحص انتهاء الوقت
}

Najm.PoliceJob = 'police'

Najm.MinCops = 0                                           -- الحد الأدنى للشرطة المتصلين لبدء المهمة

-- Mission bag/briefcase configuration (using clothing system)
Najm.MissionBag = {
    componentId = 5,    -- Parachute/Bag component slot
    drawableId = 44,    -- Bag drawable ID from clothing store
    textureId = 0       -- Texture variation (can be changed if needed)
}

-- Random delivery locations
Najm.DeliveryLocations = {
    {coords = vector3(-91.1408, 4586.104, 122.15), heading = 180.0}, -- Mount Chiliad
    {coords = vector3(2558.46, 4681.24, 33.08), heading = 45.0},     -- Grapeseed
    {coords = vector3(1728.21, 3309.55, 40.22), heading = 90.0},     -- Sandy Shores
    {coords = vector3(-1035.71, 4920.42, 173.61), heading = 270.0},  -- Mount Josiah
    {coords = vector3(2434.84, 4968.6, 42.35), heading = 135.0},     -- Paleto Bay
    {coords = vector3(-783.16, 5400.11, 33.24), heading = 0.0},      -- North Paleto
    {coords = vector3(1967.85, 4634.58, 40.10), heading = 225.0},    -- East Sandy
    {coords = vector3(-1308.42, 4394.88, 7.34), heading = 315.0},    -- West Coast
}

Najm.Missions = {
    MissionTHeBefor = {
        MissionItem = 'water',                       -- اسم الايتم اللي يجب يوصله اللاعب
        RewardItem = 'bread',                       -- اسم الايتم اللي بيدخله السوق واللي هي بطاقة السوق السوداء
        MarketLocater = 'iron',                       -- اسم الايتم الذي يحصل عليه اللاعب وهو اللي بيحدد مكان السوق السوداء
        DilevryDist = 3.0,                                      -- المسافة بين اللاعب و  DilevryPoint عشان يوصلها
        DilevryKey = 38,                                        -- الزر اللي يضغطه اللاعب لما  يكون عند   DilevryPoint  عشان يوصلها
    },
    MissonOneTest = {

    },
    MissionTwoTest = {

    }
}

